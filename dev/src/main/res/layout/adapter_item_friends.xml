<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 头像 -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/icon_default_avatar" />

    <!-- 在线状态指示器 -->
    <ImageView
        android:id="@+id/iv_online"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:background="@drawable/bg_online_friend_hint"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintEnd_toEndOf="@id/iv"
        tools:visibility="visible" />

    <!-- 状态标签 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_status"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_friend_status_online"
        android:paddingHorizontal="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_2"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:layout_constraintStart_toStartOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="Online"
        tools:visibility="visible" />

    <!-- 用户名 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_user_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="@dimen/dp_60"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:layout_constraintStart_toStartOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/tv_status"
        app:layout_goneMarginTop="@dimen/dp_4"
        tools:text="Tuski biut" />

    <!-- 游戏名称 (保留兼容性) -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_name"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="@dimen/dp_60"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#666666"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:layout_constraintStart_toStartOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/tv_user_name"
        tools:text="Game Name" />

    <!-- 空白状态提示 -->
    <include
        android:id="@+id/layout_empty_state"
        layout="@layout/layout_friends_empty_state"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>