package com.socialplay.gpark.function.pandora

import android.annotation.SuppressLint
import androidx.annotation.Keep
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle
import com.meta.pandora.Pandora
import com.socialplay.gpark.data.model.editor.AvatarLoadingStrategy
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.ui.account.startup.SelectModeFragmentArgs
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import timber.log.Timber

/**
 * xingxiu.hou
 * 2021/6/6
 * Pandora AB开关
 */
@Keep
@SuppressLint("ChineseStringLiteral")
object PandoraToggle {

    @DevPandoraToggle(name = "底层参数rsConfigArr", desc = "底层参数rsConfigArr", defValue = " ")
    private const val RS_CONFIG_ARR = "rsConfigArr"
    fun getRsConfig(): String = getValue(RS_CONFIG_ARR, " ")

    @DevPandoraToggle(
        name = "首页feed推荐libra id",
        desc = "首页feed推荐libra id \n" + " 默认：3000005",
        defValue = "3000005"
    )
    private val CONTROL_RECOMMEND_LIBRA = "control_home_feed_recommend_libra"
    fun controlRecommendLibra() = getValue(CONTROL_RECOMMEND_LIBRA, "3000005")

    @DevPandoraToggle(name = "好评引导弹窗策略", desc = "0：不打开；1：玩单个游戏弹出；2：玩角色编辑器弹出；1,2：1和2策略；默认:0,", defValue = "0,")
    private const val CONTROL_RATE_DIALOG = "control_rate_dialog"
    val positiveCommentConfig by lazyGetValue(CONTROL_RATE_DIALOG, "0,")

    @DevPandoraToggle(name = "强更开关", desc = "默认 true 开", defValue = "true")
    private const val CONTROL_UPDATE = "control_update"
    val isUpdateOpen by lazyGetValue(CONTROL_UPDATE, !com.socialplay.gpark.BuildConfig.DEBUG)

    @DevPandoraToggle(name = "用户注册性别开关设置", desc = "0:关闭；1:打开-必选；2:打开-可选；默认:1", defValue = "1")
    private const val CONTROL_SIGNUP_GENDER = "control_signup_gender"
    val isSignupGenderOpen by lazyGetValue(CONTROL_SIGNUP_GENDER, 1)

    @DevPandoraToggle(name = "引导评论弹窗首次策略", desc = "每天首次从悬浮球退出游戏，每天最多弹窗次数 默认：1", defValue = "1")
    private const val REVIEW_DIALOG_FIRST_PLAY_TIMES = "review_dialog_first_play_times"
    val reviewDialogFirstPlayTimes by lazyGetValue(REVIEW_DIALOG_FIRST_PLAY_TIMES, 1)

    @DevPandoraToggle(name = "引导评论弹窗单次时长策略", desc = "单次游玩>5分钟，每天最多弹窗次数 默认：1", defValue = "1")
    private const val REVIEW_DIALOG_ADD_UP_TIMES = "review_dialog_add_up_times"
    val reviewDialogAddUpTimes by lazyGetValue(REVIEW_DIALOG_ADD_UP_TIMES, 1)

    @DevPandoraToggle(name = "是否打开系统消息弹窗", desc = "是否打开系统消息弹窗,默认打开", defValue = "true")
    private const val CONTROL_SYSTEM_MESSAGE = "control_system_message"
    val controlSystemMessage by lazyGetValue(CONTROL_SYSTEM_MESSAGE, true)

    @DevPandoraToggle(name = "启动app是否锚定移动编辑器探索页", desc = "默认 false 关", defValue = "false")
    private const val CONTROL_ANCHORED_MOBILE = "control_anchored_mobile"
    val openWithEditor by lazyGetValue(CONTROL_ANCHORED_MOBILE, false)

    @DevPandoraToggle(name = "游戏录屏功能", desc = "默认 true 开", defValue = "true")
    private const val CONTROL_GAME_RECORD = "control_game_record"
    val openGameRecord by lazyGetValue(CONTROL_GAME_RECORD, true)

    @DevPandoraToggle(name = "社区feed新排序", desc = "默认 false 关", defValue = "false")
    private const val CONTROL_COMMUNITY_FEED_RANKING_CONTROL = "community_feed_ranking_control"
    val communityNewOrder by lazyGetValue(CONTROL_COMMUNITY_FEED_RANKING_CONTROL, false)

    const val BOTTOM_TAB_TOGGLE_DEFAULT = "2,10,9,6,1"

    @DevPandoraToggle(
        name = "底部Tab配置",
        desc = """
            1：avatar
            2：maps
            3：chat
            4：workshop
            5：profile
            6：community
            7：discover,
            8：create,
            9：add,
            10：library
            默认：$BOTTOM_TAB_TOGGLE_DEFAULT
            """,
        defValue = BOTTOM_TAB_TOGGLE_DEFAULT
    )
    private const val BOTTOM_TAB_TOGGLE = "oversea_bottom_tab_toggle"
    fun getBottomTabToggle(): String {
        return getValue(BOTTOM_TAB_TOGGLE, BOTTOM_TAB_TOGGLE_DEFAULT)
    }

    fun getTabIdList(): List<String> {
        var tabsStr = getBottomTabToggle()
        Timber.d("TAB-CONTROL tabsStr:$tabsStr")
        if (tabsStr.isNullOrBlank()) {
            tabsStr = BOTTOM_TAB_TOGGLE_DEFAULT
            Timber.d("TAB-CONTROL tabsDefaultStr:$tabsStr")
        }
        var tabIdsList: List<String> = tabsStr.replace("，", ",").split(",")
        Timber.d("TAB-CONTROL tabIdsList:$tabIdsList")
        tabIdsList = tabIdsList.filter {
            kotlin.runCatching { MainBottomNavigationItem.getItem(it.toInt()) != null }.getOrDefault(false)
        }
        if (tabIdsList.isNullOrEmpty()) {
            tabIdsList = BOTTOM_TAB_TOGGLE_DEFAULT.replace("，", ",").split(",")
            Timber.d("TAB-CONTROL tabDefaultIdsList:$tabIdsList")
        }
        tabIdsList.forEach {
            Timber.d("TAB-CONTROL $it - ${MainBottomNavigationItem.getItem(it.toInt())}")
        }
        return tabIdsList
    }

    fun isBottomTabToggle(key: String): Boolean {
        return key == BOTTOM_TAB_TOGGLE
    }

    @DevPandoraToggle(name = "是否打开用户信息收集弹框", desc = "是否打开用户信息收集弹框,默认打开", defValue = "true")
    private const val INFORMATION_COLLECTION_POPUP = "information_collection_popup"
    val isInformationCollectionPopupOpen by lazyGetValue(INFORMATION_COLLECTION_POPUP, true)

    @DevPandoraToggle(name = "是否打开充值开关", desc = "是否打开充值开关", defValue = "false")
    private const val RECHARGE_TOGGLE = "recharge_toggle"
    val isRechargeToggle by lazyGetValue(RECHARGE_TOGGLE, false)

    @DevPandoraToggle(name = "探索页拉取UGC游戏列表", desc = "默认：开 true", defValue = "true")
    private const val CONTROL_EDITOR_FETCH_UGC_GAME_LIST = "editor_fetch_ugc_game_list"
    val fetchUGCList by lazyGetValue(CONTROL_EDITOR_FETCH_UGC_GAME_LIST, true)

    @DevPandoraToggle(name = "探索页拉取PGC游戏列表", desc = "默认：开 true", defValue = "true")
    private const val CONTROL_EDITOR_FETCH_PGC_GAME_LIST = "editor_fetch_pgc_game_list"
    val fetchPGCList by lazyGetValue(CONTROL_EDITOR_FETCH_PGC_GAME_LIST, true)

    fun isVipPlusOpen(): Boolean {
        return PandoraToggleWrapper.isVipPlusOpen()
    }

    fun isVipStatusOpen(): Boolean {
        return PandoraToggleWrapper.isVipStatusOpen()
    }

    @DevPandoraToggle(name = "广告-是否开启激励视频广告", desc = "默认：false 关 ", defValue = "false")
    private const val CONTROL_REWARDED_AD_IS_ACTIVATED = "c_ad_control_award"
    val rewardedAdIsActivated by lazyGetValue(CONTROL_REWARDED_AD_IS_ACTIVATED, false)

    @DevPandoraToggle(name = "广告-是否开启广告的ts游戏黑名单", desc = "默认：,", defValue = ",")
    private const val CONTROL_BLACK_LIST = "black_list"
    val gameAdBlackList by lazyGetValue(CONTROL_BLACK_LIST, ",")


    fun isInBlacklist(gamePkg: String?): Boolean {
        if (gamePkg.isNullOrEmpty()) {
            return false
        }
        val split = gameAdBlackList.split(",").map { s ->
            s.trim()
        }.toHashSet()
        return split.contains(gamePkg)
    }

    @DevPandoraToggle(name = "每天广告播放的总次数", desc = "默认：10次", defValue = "10")
    private const val C_AD_CONTROL_AMOUNT = "c_ad_control_amount"
    val adShowAmount by lazyGetValue(C_AD_CONTROL_AMOUNT, 10)

    @DevPandoraToggle(name = "广告播放间隔", desc = "默认：60s", defValue = "60")
    private const val C_AD_CONTROL_INTERVAL = "c_ad_control_interval"
    val adShowInterval by lazyGetValue(C_AD_CONTROL_INTERVAL, 60)

    @DevPandoraToggle(name = "广告立即加载的超时时间", desc = "默认：3000ms", defValue = "3000")
    private const val C_AD_CONTROL_LOAD_TIMEOUT = "c_ad_control_timeout"
    val adLoadTimeout by lazyGetValue(C_AD_CONTROL_LOAD_TIMEOUT, 3000L)

    @DevPandoraToggle(name = "广告-是否开启广告fake功能", desc = "默认：true 开 ", defValue = "true")
    private const val control_ad_isfake = "control_ad_isfake"
    val isOpenAdFake by lazyGetValue(control_ad_isfake, true)

    @DevPandoraToggle(name = "创角-是否跳到横屏角色页", desc = "默认：关 false", defValue = "false")
    private const val CONTROL_CHOOSE_DEFAULT_ROLE_DESTINATION = "choose_default_role_destination"
    val chooseRoleJump2HorPage by lazyGetValue(CONTROL_CHOOSE_DEFAULT_ROLE_DESTINATION, false)

    @DevPandoraToggle(name = "角色截图分享", desc = "默认：开 true", defValue = "true")
    private const val CONTROL_AVATAR_SHARE = "avatar_share"
    val avatarShare = false

    @DevPandoraToggle(name = "角色tab飞轮位", desc = "默认：开 true", defValue = "true")
    private const val CONTROL_ROLE_TAB_FLY_WHEEL = "role_tab_fly_wheel"
    val roleFlyWheel by lazyGetValue(CONTROL_ROLE_TAB_FLY_WHEEL, true)

    @DevPandoraToggle(name = "超级推荐位展示形式", desc = "1. icon(默认), 2. 大图", defValue = "1")
    private const val CONTROL_SUPER_POSITION_SWITCH = "control_super_position_switch"
    val superPosition by lazyGetValue(CONTROL_SUPER_POSITION_SWITCH, 1)

    @DevPandoraToggle(name = "是否派对", desc = "true 派对, false 精选(默认)", defValue = "false")
    private const val CONTROL_IS_PARTY = "control_is_party"
    val isParty by lazyGetValue(CONTROL_IS_PARTY, false)

    @DevPandoraToggle(name = "是否开启新游Tab", desc = "true开启(默认), false关闭", defValue = "true")
    private const val CONTROL_NEW_GAMES_SHOW = "control_new_games_show"
    val isShowNewGames by lazyGetValue(CONTROL_NEW_GAMES_SHOW, true)

    @DevPandoraToggle(name = "是否开启推荐首页", desc = "true 推荐(默认), false 精选", defValue = "true")
    private const val CONTROL_IS_RECOMMEND = "control_is_recommend"
    val isHomeRecommend by lazyGetValue(CONTROL_IS_RECOMMEND, true)

    @DevPandoraToggle(name = "是否开启建造页V2", desc = "true 开启, false 关闭(默认)", defValue = "true")
    private const val CONTROL_UGC_BUILD_V2 = "control_ugc_build_v2"
    val enableUgcBuildV2 by lazyGetValue(CONTROL_UGC_BUILD_V2, true)

    @DevPandoraToggle(name = "是否开启建造页V3", desc = "GPark建造页展示版本,1：多模板配置 2：单模板配置 3：云存档模板配置", defValue = "3")
    private const val CONTROL_UGC_BUILD_VERSION = "control_ugcbuild_version"
    val enableUgcBuildVersion by lazyGetValue(CONTROL_UGC_BUILD_VERSION, "3")

    @DevPandoraToggle(name = "是否开启ugc详情页", desc = "true 开启, false 关闭(默认)", defValue = "true")
    private const val CONTROL_UGC_DETAIL = "control_ugc_detail"
    val enableUgcDetail by lazyGetValue(CONTROL_UGC_DETAIL, true)

    @DevPandoraToggle(name = "是否开启ugc游戏评论弹窗", desc = "true 开启, false 关闭(默认)", defValue = "false")
    private const val CONTROL_UGC_COMMENT_POPUP_SHOW = "ugc_comment_popup_show"
    val ugcCommentPopupShow by lazyGetValue(CONTROL_UGC_COMMENT_POPUP_SHOW, false)

    @DevPandoraToggle(name = "开启服装ugc入口", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_DESIGN = "control_avatar_design"
    val openUgcClothesEntrance by lazyGetValue(CONTROL_AVATAR_DESIGN, false)

    @DevPandoraToggle(name = "是否开启MGS名片优化开关", desc = "true 开启, false 关闭(默认)", defValue = "false")
    private const val CONTROL_MGS_CARD_OPTIMIZE = "control_mgs_card_optimize"
    val isOpenMGSCardOptimize by lazyGetValue(CONTROL_MGS_CARD_OPTIMIZE, false)

    @DevPandoraToggle(name = "开启小屋入口", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_DS_ROOM = "avatar_ds_room"
    val openUgcHomeEntrance by lazyGetValue(CONTROL_AVATAR_DS_ROOM, false)

    @DevPandoraToggle(name = "首页跟房", desc = "true为开启，false为关闭，默认为true", defValue = "false")
    private const val CONTROL_HOME_FLOW_ROOM = "control_home_flow_room"
    val openHomeFlowRoom by lazyGetValue(CONTROL_HOME_FLOW_ROOM, true)

    @DevPandoraToggle(name = "新手引导v4", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_GUIDANCE_V4 = "control_guidance_v4"
    val enableGuidanceV4 by lazyGetValue(CONTROL_GUIDANCE_V4, false)

    @DevPandoraToggle(name = "新手引导v5", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_GUIDANCE_V5 = "control_guidance_v5"
    val enableGuidanceV5 by lazyGetValue(CONTROL_GUIDANCE_V5, false)

    @DevPandoraToggle(name = "ugc首页入口", desc = "true为开启，false为关闭，默认为true", defValue = "true")
    private const val UGC_CREATE_ENTRY = "ugc_create_entry"
    val ugcCreateEntry by lazyGetValue(UGC_CREATE_ENTRY, true)

    @DevPandoraToggle(name = "ugc首页入口24小时引导次数", desc = "默认为3", defValue = "3")
    private const val UGC_CREATE_GUIDE_POPUP_TIME = "ugc_create_guide_popup_time"
    val ugcCreateGuideTimes by lazyGetValue(UGC_CREATE_GUIDE_POPUP_TIME, 3)

    @DevPandoraToggle(name = "ugc首页入口引导, 每玩到第几个游戏弹", desc = "默认为2", defValue = "2")
    private const val UGC_CREATE_GUIDE_POPUP_GAME = "ugc_create_guide_popup_game"
    val ugcCreateByGameNumbers by lazyGetValue(UGC_CREATE_GUIDE_POPUP_GAME, 2)

    @DevPandoraToggle(name = "角色页剧情创作入口", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_HALF_MOMENTS = "control_avatar_half_moments"
    val openPlotHalfHomeEntrance by lazyGetValue(CONTROL_AVATAR_HALF_MOMENTS, false)

    @DevPandoraToggle(name = "发帖页剧情创作入口", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_PLOT_HOME = "control_avatar_moments"
    val openPlotHomeEntrance by lazyGetValue(CONTROL_AVATAR_PLOT_HOME, false)

    @DevPandoraToggle(name = "设置面板按钮开关", desc = "默认：false", defValue = "false")
    private const val CONTROL_SETTING_VIEW = "control_mw_graphic_settings"
    val isOpenSettingView by lazyGetValue(CONTROL_SETTING_VIEW, false)

    @DevPandoraToggle(name = "ugc语音房", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val UGC_LIVE_ROOM = "ugc_live_room"
    val enableUgcLiveRoom by lazyGetValue(UGC_LIVE_ROOM, false)

    @DevPandoraToggle(
        name = "Wallet入口",
        desc = "0：代表开关关闭,1,2则代表了两个位置都打开1：代表avatar页面2：代表我的页面",
        defValue = "1,2"
    )
    private const val CONTROL_WALLET_ENTRANCE = "control_wallet_entrance"
    val walletEntrance by lazyGetValue(CONTROL_WALLET_ENTRANCE, "1,2")

    @DevPandoraToggle(
        name = "预加载视频流视频",
        desc = "格式为：预加载多少个视频,每个视频加载多少MB，默认为:2,0.5 表示预加载后面2个视频的前0.5MB数据",
        defValue = "2,0.5"
    )
    private const val CONTROL_VIDEO_FEED_PRELOAD = "control_video_feed_preload"
    val controlVideoFeedPreload by lazyGetValue(CONTROL_VIDEO_FEED_PRELOAD, "2,0.5")

    @DevPandoraToggle(
        name = "是否打开社区视频流Tab",
        desc = "是否打开社区视频流Tab: true为开启，false为关闭，默认为false",
        defValue = "false"
    )
    private const val CONTROL_COMMUNITY_VIDEO_TAG_SHOW_CONTROL = "community_video_tag_show_control"
    val openCommunityVideoFeedTab by lazyGetValue(CONTROL_COMMUNITY_VIDEO_TAG_SHOW_CONTROL, false)

    // https://meta.feishu.cn/wiki/M1KowRtqAiAxwAk7iwRcsczJnAb
    @DevPandoraToggle(name = "超级推荐位优化", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_GAME_SUGGESTION_OPTIMIZE = "control_gpark_poprecommend_newstyle_01"
    val gameSuggestOptimize by lazyGetValue(CONTROL_GAME_SUGGESTION_OPTIMIZE, false)

    @DevPandoraToggle(name = "弹幕样式的MGS", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val MGS_BULLET_CHAT = "mgs_bullet_chat"
    val enableBulletChat by lazyGetValue(MGS_BULLET_CHAT, false)

    @DevPandoraToggle(name = "控制Avatar页面反馈入口展示", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_FEEDBACK = "control_avatar_feedback"
    val enableAvatarFeedback by lazyGetValue(CONTROL_AVATAR_FEEDBACK, false)

    @DevPandoraToggle(name = "开启养娃入口", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_BABY = "control_avatar_baby"
    val openAvatarBaby by lazyGetValue(CONTROL_AVATAR_BABY, false)

    @DevPandoraToggle(name = "空间音效功能", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_SPACE_VOICE = "control_space_voice"
    val openSpaceVoice by lazyGetValue(CONTROL_SPACE_VOICE, false)


    @DevPandoraToggle(
        name = "推荐一行三个是否展示", desc = "0：不展示\n" +
                "1：显示精选一行三个\n" +
                "2：展示推荐一行三个", defValue = "1"
    )
    private const val CONTROL_PARTY_CONTENT_RECOMMENDATION = "control_party_content_recommendation"
    private val recommendStatus by lazyGetValue(CONTROL_PARTY_CONTENT_RECOMMENDATION, 1)

    fun isRecommendOpen(): Boolean {
        return recommendStatus == 2
    }

    fun isChoiceRecommend(): Boolean {
        return recommendStatus == 1
    }

    @DevPandoraToggle(name = "首页半屏功能开启控制", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_HALF_SCREEN = "control_half_screen"
    val openAvatarHalfScreen = false
//    val openAvatarHalfScreen by lazyGetValue(CONTROL_AVATAR_HALF_SCREEN,false)

    @DevPandoraToggle(name = "首页UGC模版展示", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AVATAR_UGCMODE = "control_avatar_ugcmode"
    val openAvatarUgcMode by lazyGetValue(CONTROL_AVATAR_UGCMODE, false)


    @DevPandoraToggle(
        name = "视频流视频发布",
        desc = "true为开启，false为关闭，默认为false",
        defValue = "false"
    )
    private const val CONTROL_VIDEO_PUBLISH_ENTRANCE = "control_video_publish_entrance"
    val isOpenVideoPublish by lazyGetValue(CONTROL_VIDEO_PUBLISH_ENTRANCE, false)


    @DevPandoraToggle(name = "模式选择开关", desc = "0：关闭功能\n1 打开功能，世界地图（横屏）在上\n2 打开功能，派对列表（竖屏）在上", defValue = "0")
    private const val CONTROL_SELECT_MODE = "control_mode_select"
    val selectMode by lazyGetValue(CONTROL_SELECT_MODE, 0)
    val hasSelectMode get() = selectMode != SelectModeFragmentArgs.MODE_0

    @DevPandoraToggle(name = "首页顶部反馈入口是否展示", desc = "true为开启，false为关闭，默认为true", defValue = "true")
    private const val CONTROL_PARTY_FEEDBACK_SWITCH = "control_party_feedback_switch"
    val openHomeFeedbackHead by lazyGetValue(CONTROL_PARTY_FEEDBACK_SWITCH, true)

    @DevPandoraToggle(name = "话题广场展示语音房", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_TOPIC_LIVEROOM = "control_topic_liveroom"
    val topicSquareShowLiveRoom by lazyGetValue(CONTROL_TOPIC_LIVEROOM, false)

    @DevPandoraToggle(name = "AIBot入口开关", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_AI_BOT = "control_app_botlist"
    val isOpenAiBot by lazyGetValue(CONTROL_AI_BOT, false)

    @DevPandoraToggle(name = "style社区开关", desc = "true为开启，false为关闭，默认为true", defValue = "true")
    private const val CONTROL_STYLE_COMMUNITY = "control_style_community"
    val enableStyleCommunity by lazyGetValue(CONTROL_STYLE_COMMUNITY, true)

    @DevPandoraToggle(name = "搜索开关", desc = "true为开启，false为关闭，默认为true", defValue = "true")
    private const val CONTROL_SEARCH = "control_search"
    val isSearchOpen by lazyGetValue(CONTROL_SEARCH, true)

    @DevPandoraToggle(name = "搜索结果页变成多tab", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_SEARCH_RESULT_MUTABLE_TAB = "control_search_result_mutable_tab"
    val isSearchResultMutableTab by lazyGetValue(CONTROL_SEARCH_RESULT_MUTABLE_TAB, false)

    @DevPandoraToggle(name = "搜索筛选功能", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val IS_SEARCH_FILTER_SHOW = "is_search_filter_show"
    val isSearchFilterShow by lazyGetValue(IS_SEARCH_FILTER_SHOW, false)

    // web用的，别删
    @DevPandoraToggle(name = "充值反馈开关", desc = "0关闭 1开启wallet的入口和返回拦截; 2开启游戏内充值入口和返回拦截;", defValue = "0,")
    private const val CONTROL_RECHARGE_SENDBACK = "control_recharge_sendback"
    val showFeedbackRecharge by lazyGetValue(CONTROL_RECHARGE_SENDBACK, "0,")


    @DevPandoraToggle(
        name = "首页视频卡片视频内容预加载",
        desc = "第一个之表示预加载多少个视频，第二个参数表示每个视频预加载多少兆，默认为0,0",
        defValue = "0,0"
    )
    private const val CONTROL_PARTY_VIDEO_PRELOAD = "control_party_video_preload"
    val controlPartyVideoPreload by lazyGetValue(CONTROL_PARTY_VIDEO_PRELOAD, "0,0")


    @DevPandoraToggle(
        name = "社区-视频tab视频内容预加载",
        desc = "第一个之表示预加载多少个视频，第二个参数表示每个视频预加载多少兆，默认为0,0",
        defValue = "0,0"
    )
    private const val CONTROL_COMMUNITY_VIDEO_PRELOAD = "control_community_video_preload"
    val controlCommunityVideoPreload by lazyGetValue(CONTROL_COMMUNITY_VIDEO_PRELOAD, "0,0")

    @DevPandoraToggle(
        name = "是否打开新版服装ugc",
        desc = "默认true; true 打开; false 关闭",
        defValue = "true"
    )
    private const val CONTROL_CLOTHES_UGC_V2_OPEN = "control_clothesUgcV2_open"
    val openClothesUgcV2 by lazyGetValue(CONTROL_CLOTHES_UGC_V2_OPEN, true)

    @DevPandoraToggle(
        name = "ts游戏资源存储自动清理开关",
        desc = "false：关闭。true：开启",
        defValue = "false"
    )
    private const val CONTROL_MW_STORAGE_AUTO_CLEANUP = "control_mw_storage_auto_cleanup"
    val mwStorageAutoCleanup by lazyGetValue(CONTROL_MW_STORAGE_AUTO_CLEANUP, false)

    @DevPandoraToggle(
        name = "角色前台常驻通知",
        desc = "角色前台常驻通知 默认false:打开 true:打开",
        defValue = "false"
    )
    private const val CONTROL_AVATAR_PROCESS_NOTICE = "control_avatar_process_notice"
    val isOpenAvatarForegroundNotification by lazyGetValue(CONTROL_AVATAR_PROCESS_NOTICE, false)


    @DevPandoraToggle(
        name = "增加IM互动提示+入口",
        desc = "false 关闭，默认：true:开启",
        defValue = "true"
    )
    private const val CONTROL_IM_ENTRANCE = "control_im_ups_entrance_add"
    val isIMEntrance by lazyGetValue(CONTROL_IM_ENTRANCE, true)

    @DevPandoraToggle(name = "是否开启预下载", desc = ">=0 为开启，<0 为关闭，默认为 -1", defValue = "-1")
    private const val CONTROL_TS_GAME_PRE_DOWNLOAD = "control_ts_game_pre_download"
    val tsGamePreDownloadSecond by lazyGetValue(CONTROL_TS_GAME_PRE_DOWNLOAD, -1)

    @DevPandoraToggle(name = "PGC游戏列表", desc = "默认为开:true 关闭:false", defValue = "true")
    private const val CONTROL_AVATAR_GAMELIST = "control_avatar_gamelist"
    val isOpenAvatarGameList by lazyGetValue(CONTROL_AVATAR_GAMELIST, true)

    @DevPandoraToggle(name = "Moment拍剧模板", desc = "默认为开:true 关闭:false", defValue = "true")
    private const val CONTROL_AVATAR_MOMENTLIST = "control_avatar_momentlist"
    val isOpenAvatarMomentList by lazyGetValue(CONTROL_AVATAR_MOMENTLIST, true)

    @DevPandoraToggle(name = "精选页增加悬浮ICON入口", desc = "默认为关:false 开启:true", defValue = "false")
    private const val CONTROL_ROBUX_ACTIVITY = "control_robux_activity"
    val isOpenRoBuxRecord by lazyGetValue(CONTROL_ROBUX_ACTIVITY, false)

    @DevPandoraToggle(name = "领奖次数配置", desc = "默认为 0", defValue = "0")
    private const val CONTROL_ROBUX_NUMBER = "control_robux_number"
    val getNumberRoBux by lazyGetValue(CONTROL_ROBUX_NUMBER, 0)

    @DevPandoraToggle(
        name = "角色加载网络优化",
        desc = "默认旧版本:0\n" +
                "带优化1、2、3:1\n" +
                "带优化4:2 \n" +
                "带优化1、2、3、4:3",
        defValue = "0"
    )
    private const val CONTROL_AVATAR_LOADING = "control_avatar_network"
    val avatarLoadingStrategy by lazy {
        AvatarLoadingStrategy.create(getValue(CONTROL_AVATAR_LOADING, 0))
    }

    @DevPandoraToggle(name = "控制im好友聊天通知权限的开关", desc = "true为开启，false为关闭，默认为false", defValue = "false")
    private const val CONTROL_CHAT_PUSH_NOTIFICATION = "control_chat_push_notifications"
    val isChatPushNotification by lazyGetValue(CONTROL_CHAT_PUSH_NOTIFICATION, false)

    @DevPandoraToggle(name = "控制帖子互动通知权限的频率的实验数据m", desc = "配置10 ，代表num=10。num=-1表明不发送弹窗。", defValue = "-1")
    private const val CONTROL_POST_INTERACT_PUSH_NOTIFICATION_FREQUENCY = "control_post_interact_push_notifications_frequency"
    val postInteractPushNotificationFrequency by lazyGetValue(CONTROL_POST_INTERACT_PUSH_NOTIFICATION_FREQUENCY, -1)

    @DevPandoraToggle(
        name = "引导通知权限开关",
        desc = "true为开启，false为关闭，默认为false",
        defValue = "false"
    )
    private const val CONTROL_SYSTEM__NOTIFICATION = "control_steer_system_notification"
    val isSystemNotification by lazyGetValue(CONTROL_SYSTEM__NOTIFICATION, false)


    @DevPandoraToggle(
        name = "profile页是否开启ugc作品tab",
        desc = "开启true，关闭false，默认为true",
        defValue = "true"
    )
    private const val CONTROL_PROFILE_UGC = "control_profile_ugc"
    val openProfileUgc by lazyGetValue(CONTROL_PROFILE_UGC, true)

    @DevPandoraToggle(
        name = "ts游戏资源存储自动清理分配的上限额度",
        desc = "设备存储空间GB_MW存储上限GB",
        defValue = "0_2,14_2,30_3,60_4"
    )
    private const val CONTROL_MW_STORAGE_AUTO_LIMIT = "control_mw_storage_auto_limit"
    val mwStorageAutoLimit by lazyGetValue(
        CONTROL_MW_STORAGE_AUTO_LIMIT,
        "0_2,14_2,30_3,60_4"
    )

    @DevPandoraToggle(
        name = "七日签到提醒开关",
        desc = "控制用户侧是否展示七日签到提醒的开关, 默认关闭false",
        defValue = "false"
    )
    private const val CONTROL_SEVEN_DAY_SIGN_IN_REMIND = "control_dailybonus_show_tips"
    private val _enableSevenDaySignInRemind by lazyGetValue(CONTROL_SEVEN_DAY_SIGN_IN_REMIND, false)
    val enableSevenDaySignInRemind get() = PayProvider.ENABLE_DAILY && _enableSevenDaySignInRemind

    @DevPandoraToggle(
        name = "mgs迁移至mw",
        desc = "true为开启，false为关闭，true",
        defValue = "true"
    )
    private const val CONTROL_MW_MGS = "control_mw_mgs"
    val isMWMgs by lazyGetValue(CONTROL_MW_MGS, true)

    //region share
    private const val DEFAULT_SHARE_CONFIG = "1,2,3,4,5,6,7"
    private const val SHARE_DISABLE = "0"
    private const val SHARE_PROFILE = "1"
    private const val SHARE_VIDEO_FEED = "2"
    private const val SHARE_POST = "3"
    private const val SHARE_PGC = "4"
    private const val SHARE_UGC = "5"
    private const val SHARE_OC_MOMENT = "6"
    private const val SHARE_SCREENSHOT = "7"

    @DevPandoraToggle(
        name = "分享入口展示开关",
        desc = "0全部关闭\n1个人主页\n2视频流\n3帖子\n4pgc\n5ugc\n6短剧\n7截图",
        defValue = DEFAULT_SHARE_CONFIG
    )
    private const val CONTROL_SHARE_SCENE = "control_share_scene"
    private val controlShareScene by lazyGetValue(CONTROL_SHARE_SCENE, DEFAULT_SHARE_CONFIG)

    private var shareInit = false

    private fun initShare() {
        if (shareInit) return
        shareInit = true
        val platforms = controlShareScene.split(",")
        for (platform in platforms) {
            when (platform) {
                SHARE_DISABLE -> {
                    enableShareProfile = false
                    enableShareVideoFeed = false
                    enableSharePost = false
                    enableSharePgc = false
                    enableShareUgc = false
                    enableShareOcMoment = false
                    enableShareScreenshot = false
                    break
                }

                SHARE_PROFILE -> {
                    enableShareProfile = true
                }

                SHARE_VIDEO_FEED -> {
                    enableShareVideoFeed = true
                }

                SHARE_POST -> {
                    enableSharePost = true
                }

                SHARE_PGC -> {
                    enableSharePgc = true
                }

                SHARE_UGC -> {
                    enableShareUgc = true
                }

                SHARE_OC_MOMENT -> {
                    enableShareOcMoment = true
                }

                SHARE_SCREENSHOT -> {
                    enableShareScreenshot = true
                }
            }
        }
    }

    var enableShareProfile: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableShareVideoFeed: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableSharePost: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableSharePgc: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableShareUgc: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableShareOcMoment: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    var enableShareScreenshot: Boolean = false
        get() {
            initShare()
            return field
        }
        private set

    @DevPandoraToggle(
        name = "发言时提示设置账密V7",
        desc = "控制发言时是否提示设置账密V7，默认true",
        defValue = "true"
    )
    private const val PUBLISH_SETUP_ACCOUNT_SWITCH = "publish_setup_account_switch"
    val enablePublishSetupAccount by lazyGetValue(PUBLISH_SETUP_ACCOUNT_SWITCH, true)
    //endregion


    @DevPandoraToggle(
        name = "消息提示关闭入口展示",
        desc = "默认false ，展示当前线上状态：im消息弹窗上没有“免打扰”按钮,true：在im消息弹窗上下发“免打扰”icon按钮并在【设置】列表新增“允许消息提醒”的开关",
        defValue = "true"
    )
    private const val CONTROL_IM_TIP_CLOSE = "control_im_ups_cancel_add"
    val isIMTipsClose by lazyGetValue(CONTROL_IM_TIP_CLOSE, true)

    @DevPandoraToggle(name = "新消息中心样式(消息中心优化)", desc = "默认：false 关闭， true 打开", defValue = "false")
    private const val NEW_STYLE_MESSAGE = "new_style_message"
    val isNewMessage by lazyGetValue(NEW_STYLE_MESSAGE, false)

    @DevPandoraToggle(name = "移动编辑器-ugc备份", desc = "默认 true 开", defValue = "true")
    private const val CONTROL_EDITOR_UGC_BACKUP = "control_editor_ugc_backup"
    val isUgcBackup by lazyGetValue(CONTROL_EDITOR_UGC_BACKUP, true)

    @DevPandoraToggle(name = "移动编辑器-ugc备份-不删除发布作品", desc = "默认 true 开", defValue = "true")
    private const val CONTROL_EDITOR_UGC_BACKUP_NOT_DELETE_PUBLISH = "control_editor_ugc_backup_not_delete_publish"
    val isUgcBackupNotDeletePublish by lazyGetValue(CONTROL_EDITOR_UGC_BACKUP_NOT_DELETE_PUBLISH, true)


    @DevPandoraToggle(
        name = "控制签到弹窗",
        desc = "0：关闭；1：只弹1次；2：没完成签到就弹；",
        defValue = "0"
    )
    private const val CONTROL_DAILYBONUS_SHOW_POPUP = "control_dailybonus_show_popup"
    private val _dailySignDialogPopup by lazyGetValue(CONTROL_DAILYBONUS_SHOW_POPUP, 0)
    val dailySignDialogPopup
        get() = if (PayProvider.ENABLE_DAILY) {
            _dailySignDialogPopup
        } else {
            0
        }

    @DevPandoraToggle(
        name = "穿搭分享功能开关",
        desc = "true为开启，false为关闭，默认为false",
        defValue = "false"
    )
    private const val ENABLE_POST_SHARE_OUTFIT = "control_tryon_share"
    val enableTryOnShare by lazyGetValue(ENABLE_POST_SHARE_OUTFIT, true)

    @DevPandoraToggle(name = "个人主页素材库底栏开关", desc = "true 展示（默认）; false 不展示", defValue = "true")
    private const val MY_POFILE_LIBRARY_TAB_SHOW = "my_pofile_library_tab_show"
    val enableProfileLibraryTab by lazyGetValue(MY_POFILE_LIBRARY_TAB_SHOW, true)

    @DevPandoraToggle(name = "个人主页装备栏展示", desc = "true 展示（默认）; false 不展示", defValue = "true")
    private const val MY_POFILE_EQUIPMENT_SHOW = "my_pofile_equipment_show"
    val enableProfileCurrentClothes by lazyGetValue(MY_POFILE_EQUIPMENT_SHOW, true)

    @DevPandoraToggle(
        name = "控制好友申请消息弹窗的下发",
        desc = "true开（默认） false关",
        defValue = "true"
    )
    private const val CONTROL_FRIEND_REQUESTS_UPS = "control_friend_requests_ups"
    val enableFriendRequestPopUp by lazyGetValue(CONTROL_FRIEND_REQUESTS_UPS, true)

    @DevPandoraToggle(
        name = "KOL认证标志是否展示",
        desc = "true为开启，false为关闭，默认为false",
        defValue = "false"
    )
    private const val CONTROL_KOL_ICON = "control_kol_icon"
    val isShowCreatorLabel by lazyGetValue(CONTROL_KOL_ICON, false)

    @DevPandoraToggle(
        name = "UGC点亮功能",
        desc = "true为开启，false为关闭，默认：false",
        defValue = "false"
    )
    private const val CONTROL_LIGHT_UP = "control_light_up"
    val enableLightUp by lazyGetValue(CONTROL_LIGHT_UP, false)

    @DevPandoraToggle(name = "建造新手引导界面开关", desc = "true 展示; false 不展示（默认）", defValue = "false")
    private const val BUILD_NEWBIE_GUIDE_SWITCH = "build_newbie_guide_switch"
    val enableBuildNewbieGuide by lazyGetValue(BUILD_NEWBIE_GUIDE_SWITCH, false)

    @DevPandoraToggle(name = "新手引导-性别选择界面开关", desc = "true 展示(默认); false 不展示", defValue = "true")
    private const val BUILD_NEWBIE_GUIDE_SEX = "build_newbie_guide_sex"
    val enableNewbieGuideSex by lazyGetValue(BUILD_NEWBIE_GUIDE_SEX, true)

    @DevPandoraToggle(name = "新手引导-年龄界面开关", desc = "true 展示; false 不展示(默认)", defValue = "false")
    private const val BUILD_NEWBIE_GUIDE_BIRTHDAY = "build_newbie_guide_birthday"
    val enableNewbieGuideBirthday by lazyGetValue(BUILD_NEWBIE_GUIDE_BIRTHDAY, false)

    @DevPandoraToggle(name = "新手引导-昵称界面开关", desc = "true 展示(默认); false 不展示", defValue = "true")
    private const val BUILD_NEWBIE_GUIDE_NAME = "build_newbie_guide_name"
    val enableNewbieGuideName by lazyGetValue(BUILD_NEWBIE_GUIDE_NAME, true)

    @DevPandoraToggle(name = "新手引导-兴趣选择/创作主题界面开关", desc = "true 展示(默认); false 不展示", defValue = "true")
    private const val BUILD_NEWBIE_GUIDE_CREATE_THEME = "build_newbie_guide_create_theme"
    val enableNewbieGuideCreateTheme by lazyGetValue(BUILD_NEWBIE_GUIDE_CREATE_THEME, true)

    @DevPandoraToggle(
        name = "派对-社区-探索页面是否显示",
        desc = "true为显示，false为关闭，默认：false",
        defValue = "false"
    )
    private const val CONTROL_DISCOVER_OPEN = "control_discover_open"
    val showPartyCommunityDiscover by lazyGetValue(CONTROL_DISCOVER_OPEN, false)

    @DevPandoraToggle(
        name = "创建群聊入口展示",
        desc = "true为显示，false为不显示，默认：false",
        defValue = "true"
    )
    private const val CREATE_GROUP_SHOW = "create_group_show"
    val showCreateGroupMenu by lazyGetValue(CREATE_GROUP_SHOW, true)

    @DevPandoraToggle(
        name = "个人主页群聊入口展示",
        desc = "true为显示，false为不显示，默认：false",
        defValue = "true"
    )
    private const val USER_PROFILE_GROUP_LIST_SHOW = "user_profile_group_list_show"
    val showProfileGroupChatLayout by lazyGetValue(USER_PROFILE_GROUP_LIST_SHOW, true)

    @DevPandoraToggle(
        name = "群聊申请tab展示",
        desc = "true为显示，false为不显示，默认：false",
        defValue = "true"
    )
    private const val GROUP_REQUEST_TAB_SHOW = "group_request_tab_show"
    val showGroupRequestJoinTab by lazyGetValue(GROUP_REQUEST_TAB_SHOW, true)

    /**
     * 关闭的时候赠花客户端这边不展示入口和弹窗等逻辑。
     */
    @DevPandoraToggle(
        name = "赠花功能是否开启",
        desc = "ture：打开赠花功能，false：关闭赠花功能，默认：false",
        defValue = "false"
    )
    private const val CONTROL_GAME_GIFT_OPEN = "control_game_gift_open"
    val enableGameGiftOption by lazyGetValue(CONTROL_GAME_GIFT_OPEN, false)

    //region share
    private const val DEFAULT_MODULE_GUIDE_CONFIG = "1,2,3,4,5,6"
    const val MODULE_GUIDE_FIRST_ROOKIE_TAB = "1"
    const val MODULE_GUIDE_FIRST_ASSET_INTERACT = "2"
    const val MODULE_GUIDE_LABEL_CLICK = "3"
    const val MODULE_GUIDE_TEMPLATE_PAGE = "4"
    const val MODULE_GUIDE_ROOKIE_BTN = "5"
    const val MODULE_GUIDE_PROFILE_ASSET_TAB = "6"

    @DevPandoraToggle(
        name = "分享入口展示开关",
        desc = "1新人专区tab|2首次点赞评论获取|3点击勋章|4进入模板页|5资源tab按钮|6个人素材库为空",
        defValue = DEFAULT_MODULE_GUIDE_CONFIG
    )
    private const val GUIDE_DIALOG_SHOW = "guide_dialog_show"
    private val guideDialogShow by lazyGetValue(GUIDE_DIALOG_SHOW, DEFAULT_MODULE_GUIDE_CONFIG)

    private var guideDialogShowInit = false

    @DevPandoraToggle(
        name = "引导去商店评论",
        desc = "true为展示，false为不展示，默认是false不展示",
        defValue = "false"
    )
    private const val GUIDE_STORE_COMMENT_DIALOG_SHOW = "control_app_evaluate"
    val guideStoreCommentDialog by lazyGetValue(GUIDE_STORE_COMMENT_DIALOG_SHOW, false)

    @DevPandoraToggle(
        name = "未发布游戏提醒召回弹框",
        desc = "true为展示，false为不展示，默认是true展示",
        defValue = "true"
    )
    private const val CONTROL_UNPUBLISHED_DIALOG = "control_unpublished_dialog"
    val unpublishedDialog by lazyGetValue(CONTROL_UNPUBLISHED_DIALOG, true)

    private fun initGuideDialogShow() {
        if (guideDialogShowInit) return
        guideDialogShowInit = true
        val scenes = guideDialogShow.split(",")
        for (scene in scenes) {
            when (scene) {
                MODULE_GUIDE_FIRST_ROOKIE_TAB -> {
                    enableModuleGuideRookieTab = true
                }

                MODULE_GUIDE_FIRST_ASSET_INTERACT -> {
                    enableModuleGuideFirstInteract = true
                }

                MODULE_GUIDE_LABEL_CLICK -> {
                    enableModuleGuideClickLabel = true
                }

                MODULE_GUIDE_TEMPLATE_PAGE -> {
                    enableModuleGuideTemplatePage = true
                }

                MODULE_GUIDE_ROOKIE_BTN -> {
                    enableModuleGuideAssetBtn = true
                }

                MODULE_GUIDE_PROFILE_ASSET_TAB -> {
                    enableModuleGuideProfileAssetTabEmpty = true
                }
            }
        }
    }

    fun isModuleGuideEnable(type: String): Boolean {
        return when (type) {
            MODULE_GUIDE_FIRST_ROOKIE_TAB -> {
                enableModuleGuideRookieTab
            }

            MODULE_GUIDE_FIRST_ASSET_INTERACT -> {
                enableModuleGuideFirstInteract
            }

            MODULE_GUIDE_LABEL_CLICK -> {
                enableModuleGuideClickLabel
            }

            MODULE_GUIDE_TEMPLATE_PAGE -> {
                enableModuleGuideTemplatePage
            }

            MODULE_GUIDE_ROOKIE_BTN -> {
                enableModuleGuideAssetBtn
            }

            MODULE_GUIDE_PROFILE_ASSET_TAB -> {
                enableModuleGuideProfileAssetTabEmpty
            }

            else -> {
                false
            }
        }
    }

    var enableModuleGuideRookieTab: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set

    var enableModuleGuideFirstInteract: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set

    var enableModuleGuideClickLabel: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set

    var enableModuleGuideTemplatePage: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set

    var enableModuleGuideAssetBtn: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set

    var enableModuleGuideProfileAssetTabEmpty: Boolean = false
        get() {
            initGuideDialogShow()
            return field
        }
        private set
    //endregion

    @DevPandoraToggle(name = "是否启用新的Babel web支持", desc = "默认true", defValue = "true")
    private const val CONTROL_BABEL_WEB = "control_babel_web"
    val isOpenBabelWeb by lazyGetValue(CONTROL_BABEL_WEB, true)

    @DevPandoraToggle(
        name = "控制TAB里面的WebView是否启用缓存",
        desc = "控制TAB里面的WebView是否启用缓存 默认false",
        defValue = "false"
    )
    private const val CONTROL_WEBVIEW_TAB_CACHE = "control_webview_tab_cache"
    val isOpenTabWebContentCache by lazyGetValue(CONTROL_WEBVIEW_TAB_CACHE, false)

    private inline fun <reified T> lazyGetValue(key: String, default: T): Lazy<T> {
        return lazy { getValue(key, default) }
    }

    /**
     * 获取开关的值
     * @param key 开关Key
     * @param defaultValue 默认开关值
     * @return T 获取开关的结果
     *
     */
    private fun <T> getValue(key: String, defaultValue: T): T {
        return if (DeveloperPandoraToggle.isEnable()) {
            DeveloperPandoraToggle.getValue(key, defaultValue) ?: defaultValue
        } else {
            Pandora.getAbConfig(key, defaultValue)
        }
    }
}