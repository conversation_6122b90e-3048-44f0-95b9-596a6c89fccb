package com.socialplay.gpark.ui.editorschoice.header.friends

import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfoDiff
import com.socialplay.gpark.databinding.AdapterItemFriendsBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.R
import androidx.core.view.isVisible

/**
 * 2023/8/11
 */
class ChoiceHomeHeaderFriendsAdapter(
    private val glide: RequestManager,
    private val itemWidth: Int
) :
    BaseDifferAdapter<ChoiceFriendInfo, AdapterItemFriendsBinding>(ChoiceFriendInfoDiff) {

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterItemFriendsBinding {
        return AdapterItemFriendsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterItemFriendsBinding>,
        item: ChoiceFriendInfo,
    ) {
        if (item.isAdd()) {
            showAddItem(holder.binding, item)
        } else {
            showUserItem(holder.binding, item)
        }
    }

    private fun showAddItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        // 隐藏用户相关的视图
        binding.iv.isVisible = false
        binding.ivOnline.isVisible = false
        binding.tvUserName.isVisible = false
        binding.tvGameName.isVisible = false
        binding.tvStatus.isVisible = false

        // 显示空白状态布局
        binding.layoutEmptyState.isVisible = item.showHint

        binding.root.updateLayoutParams<MarginLayoutParams> {
            marginStart = 5.dp
            marginEnd = 12.dp
        }
    }

    private fun showUserItem(binding: AdapterItemFriendsBinding, item: ChoiceFriendInfo) {
        // 隐藏空白状态布局
        binding.layoutEmptyState.isVisible = false

        // 显示用户相关的视图
        binding.iv.isVisible = true
        binding.tvUserName.isVisible = true

        binding.root.updateLayoutParams<MarginLayoutParams> {
            marginEnd = 12.dp
        }

        // 加载头像
        glide.load(item.userAvatar)
            .placeholder(item.placeholderIcon)
            .into(binding.iv)

        // 设置用户名
        binding.tvUserName.text = item.userName

        // 处理状态显示
        val context = binding.root.context
        if (item.isInGame()) {
            // 游戏中状态
            binding.ivOnline.isVisible = false
            binding.tvStatus.apply {
                isVisible = true
                text = context.getString(R.string.friend_status_gaming)
                setBackgroundResource(R.drawable.bg_friend_status_gaming)
            }
            binding.tvGameName.apply {
                isVisible = true
                text = item.gameName
            }
        } else if (item.isOnline()) {
            // 在线状态
            binding.ivOnline.isVisible = true
            binding.tvStatus.apply {
                isVisible = true
                text = context.getString(R.string.friend_status_online)
                setBackgroundResource(R.drawable.bg_friend_status_online)
            }
            binding.tvGameName.isVisible = false
        } else {
            // 离线状态
            binding.ivOnline.isVisible = false
            binding.tvStatus.apply {
                isVisible = true
                text = context.getString(R.string.friend_status_offline)
                setBackgroundResource(R.drawable.bg_friend_status_offline)
            }
            binding.tvGameName.isVisible = false
        }
    }
}